<!-- 客户审核管理页面 -->
<style>
/* 修复模态框遮罩问题 */
.modal-backdrop {
    transition: opacity 0.15s linear;
}

.modal-backdrop.fade {
    opacity: 0;
}

.modal-backdrop.show {
    opacity: 0.5;
}

/* 确保模态框居中显示 */
.modal-dialog-centered {
    display: flex;
    align-items: center;
    min-height: calc(100% - 1rem);
}

@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}

/* 防止遮罩层堆叠 */
body.modal-open {
    overflow: hidden;
}

/* 按钮加载状态样式 */
.btn:disabled {
    opacity: 0.65;
    cursor: not-allowed;
}

/* 搜索框样式 */
.input-group-sm .form-control {
    border-radius: 0.375rem 0 0 0.375rem;
}

.input-group-sm .input-group-text {
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

/* 搜索框焦点效果 */
#serverSearchInput:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* 分页样式 */
.pagination-sm .page-link {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* 排序按钮样式 */
.sort-btn {
    transition: all 0.2s ease;
    border-radius: 20px;
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}
.sort-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.sort-btn.active {
    background-color: var(--bs-primary) !important;
    border-color: var(--bs-primary) !important;
    color: white !important;
    box-shadow: 0 2px 8px rgba(0,123,255,0.3);
}
.sort-btn.btn-outline-success.active {
    background-color: var(--bs-success) !important;
    border-color: var(--bs-success) !important;
    box-shadow: 0 2px 8px rgba(40,167,69,0.3);
}
</style>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="bi bi-person-check"></i> 客户审核管理</h2>
            <p class="text-muted">管理客户审核链接和监控审核进度</p>
        </div>
        <div class="col-md-4 text-end">
            <button type="button" class="btn btn-outline-secondary" onclick="location.reload()">
                <i class="bi bi-arrow-clockwise"></i> 刷新页面
            </button>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-primary">{{ client_data|length }}</h4>
                    <small class="text-muted">总客户数</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-success">{{ client_data|selectattr('has_active_link')|list|length }}</h4>
                    <small class="text-muted">有效链接</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-info">{{ client_data|map(attribute='pending_count')|sum }}</h4>
                    <small class="text-muted">待客户审核</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-info">{{ client_data|map(attribute='reviewed_count')|sum }}</h4>
                    <small class="text-muted">已审核完成</small>
                </div>
            </div>
        </div>
    </div>

    <!-- 客户列表 -->
    <div class="card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h6 class="card-title mb-0">客户审核状态</h6>
                </div>
                <div class="col-md-6">
                    <div class="d-flex align-items-center gap-2">
                        <!-- 排序说明 -->
                        <small class="text-muted me-1">排序:</small>

                        <!-- 排序标签 -->
                        <div class="btn-group btn-group-sm" role="group" aria-label="排序选项">
                            <button type="button" class="btn btn-outline-primary sort-btn {{ 'active' if sort_by == 'pending_desc' else '' }}"
                                    data-sort="pending_desc" title="按待审核数量从多到少排序">
                                <i class="bi bi-clock"></i> 待审核
                            </button>
                            <button type="button" class="btn btn-outline-success sort-btn {{ 'active' if sort_by == 'reviewed_desc' else '' }}"
                                    data-sort="reviewed_desc" title="按已审核数量从多到少排序">
                                <i class="bi bi-check-circle"></i> 已审核
                            </button>
                        </div>

                        <!-- 排序提示 -->
                        <small class="text-muted ms-1">
                            {% if sort_by == 'reviewed_desc' %}
                                (按已审核数量↓)
                            {% else %}
                                (按待审核数量↓)
                            {% endif %}
                        </small>

                        <!-- 搜索框 -->
                        <form method="GET" action="{{ url_for('main_simple.client_review') }}" class="d-flex flex-grow-1" id="searchForm">
                            <input type="hidden" name="sort_by" id="sortByInput" value="{{ sort_by or '' }}">
                            <div class="input-group input-group-sm">
                                <span class="input-group-text">
                                    <i class="bi bi-search"></i>
                                </span>
                                <input type="text" class="form-control" name="search" id="serverSearchInput"
                                       placeholder="搜索客户名称..." value="{{ search or '' }}" autocomplete="off">
                                <button class="btn btn-outline-primary" type="submit">
                                    <i class="bi bi-search"></i>
                                </button>
                                {% if search %}
                                <a href="{{ url_for('main_simple.client_review') }}" class="btn btn-outline-secondary">
                                    <i class="bi bi-x"></i>
                                </a>
                                {% endif %}
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            {% if client_data %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="120">客户信息</th>
                                <th width="90" class="text-center">审核设置</th>
                                <th width="80" class="text-center">每日篇数</th>
                                <th width="70" class="text-center">待审核</th>
                                <th width="70" class="text-center">已审核</th>
                                <th width="70" class="text-center">分享数量</th>
                                <th width="120">操作</th>
                                <th width="80" class="text-center">创建时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in client_data %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div>
                                            <h6 class="mb-1">{{ item.client.name }}</h6>
                                            {% if item.client.description %}
                                            <small class="text-muted">{{ item.client.description }}</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td class="text-center">
                                    {% if item.client.auto_approved %}
                                        <span class="badge bg-info">自动审核</span>
                                    {% else %}
                                        <span class="badge bg-primary">需要审核</span>
                                    {% endif %}
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-warning text-dark fs-6">{{ item.client.daily_content_count or 0 }}</span>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-info fs-6">{{ item.pending_count }}</span>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-success fs-6">{{ item.reviewed_count }}</span>
                                </td>
                                <td class="text-center">
                                    {% if item.active_links %}
                                        <span class="badge bg-primary fs-6">{{ item.active_links|length }}</span>
                                    {% else %}
                                        <span class="badge bg-secondary fs-6">0</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group-vertical btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-success btn-sm"
                                                onclick="createShareLink({{ item.client.id }})">
                                            <i class="bi bi-share"></i> 分享
                                        </button>
                                    </div>
                                </td>
                                <td class="text-center">
                                    <small class="text-muted">
                                        {{ item.client.created_at.strftime('%Y-%m-%d') if item.client.created_at else '未知' }}
                                    </small>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- 翻页组件 -->
                {% if pagination %}
                <div class="card-footer">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <form method="GET" action="{{ url_for('main_simple.client_review') }}" class="d-flex align-items-center">
                                {% if search %}
                                <input type="hidden" name="search" value="{{ search }}">
                                {% endif %}
                                {% if sort_by %}
                                <input type="hidden" name="sort_by" value="{{ sort_by }}">
                                {% endif %}
                                <label class="form-label mb-0 me-2 text-nowrap" style="font-size: 0.875rem;">每页:</label>
                                <select name="per_page" class="form-select form-select-sm" onchange="this.form.submit()" style="width: auto;">
                                    <option value="10" {% if pagination.per_page == 10 %}selected{% endif %}>10</option>
                                    <option value="20" {% if pagination.per_page == 20 %}selected{% endif %}>20</option>
                                    <option value="50" {% if pagination.per_page == 50 %}selected{% endif %}>50</option>
                                    <option value="100" {% if pagination.per_page == 100 %}selected{% endif %}>100</option>
                                </select>
                            </form>
                        </div>
                        <div class="col-md-4 text-center">
                            <small class="text-muted">
                                显示第 {{ pagination.per_page * (pagination.page - 1) + 1 }} -
                                {{ pagination.per_page * (pagination.page - 1) + pagination.items|length }} 条，
                                共 {{ pagination.total }} 条记录
                            </small>
                        </div>
                        <div class="col-md-4">
                            <nav aria-label="客户列表分页">
                                <ul class="pagination pagination-sm justify-content-end mb-0">
                                    <!-- 首页 -->
                                    {% if pagination.has_prev %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('main_simple.client_review', page=1, search=search, per_page=pagination.per_page, sort_by=sort_by) }}">
                                            <i class="bi bi-chevron-double-left"></i>
                                        </a>
                                    </li>
                                    <!-- 上一页 -->
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('main_simple.client_review', page=pagination.prev_num, search=search, per_page=pagination.per_page, sort_by=sort_by) }}">
                                            <i class="bi bi-chevron-left"></i>
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link"><i class="bi bi-chevron-double-left"></i></span>
                                    </li>
                                    <li class="page-item disabled">
                                        <span class="page-link"><i class="bi bi-chevron-left"></i></span>
                                    </li>
                                    {% endif %}

                                    <!-- 页码 -->
                                    {% for page_num in pagination.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                                        {% if page_num %}
                                            {% if page_num != pagination.page %}
                                            <li class="page-item">
                                                <a class="page-link" href="{{ url_for('main_simple.client_review', page=page_num, search=search, per_page=pagination.per_page, sort_by=sort_by) }}">{{ page_num }}</a>
                                            </li>
                                            {% else %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ page_num }}</span>
                                            </li>
                                            {% endif %}
                                        {% else %}
                                        <li class="page-item disabled">
                                            <span class="page-link">…</span>
                                        </li>
                                        {% endif %}
                                    {% endfor %}

                                    <!-- 下一页 -->
                                    {% if pagination.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('main_simple.client_review', page=pagination.next_num, search=search, per_page=pagination.per_page, sort_by=sort_by) }}">
                                            <i class="bi bi-chevron-right"></i>
                                        </a>
                                    </li>
                                    <!-- 末页 -->
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('main_simple.client_review', page=pagination.pages, search=search, per_page=pagination.per_page, sort_by=sort_by) }}">
                                            <i class="bi bi-chevron-double-right"></i>
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link"><i class="bi bi-chevron-right"></i></span>
                                    </li>
                                    <li class="page-item disabled">
                                        <span class="page-link"><i class="bi bi-chevron-double-right"></i></span>
                                    </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    {% if search %}
                        <i class="bi bi-search fs-1 text-muted"></i>
                        <h5 class="text-muted mt-3">未找到匹配的客户</h5>
                        <p class="text-muted">搜索 "{{ search }}" 没有找到相关客户</p>
                        <a href="{{ url_for('main_simple.client_review') }}" class="btn btn-outline-primary">
                            <i class="bi bi-arrow-left"></i> 查看所有客户
                        </a>
                    {% else %}
                        <i class="bi bi-people fs-1 text-muted"></i>
                        <h5 class="text-muted mt-3">暂无客户</h5>
                        <p class="text-muted">请先添加客户信息</p>
                        <button type="button" class="btn btn-outline-primary" onclick="showPage('clients')">
                            <i class="bi bi-arrow-left"></i> 去客户管理
                        </button>
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 分享链接详情模态框 -->
<div class="modal fade" id="shareLinkModal" tabindex="-1">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">分享链接详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="shareLinkModalBody">
                <!-- 内容将通过JavaScript动态加载 -->
            </div>
        </div>
    </div>
</div>

<!-- 创建分享链接模态框 -->
<div class="modal fade" id="createLinkModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">分享链接</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createLinkForm">
                    <!-- 任务选择 -->
                    <div class="mb-3">
                        <label for="linkTaskId" class="form-label">选择任务</label>
                        <select class="form-select" id="linkTaskId">
                            <option value="">所有任务</option>
                            <!-- 任务选项将通过JavaScript动态加载 -->
                        </select>
                        <div class="form-text">选择特定任务或留空表示所有任务</div>
                    </div>

                    <!-- 有效期选择 -->
                    <div class="mb-3">
                        <label class="form-label">有效期</label>
                        <div class="mb-2">
                            <div class="btn-group w-100" role="group">
                                <button type="button" class="btn btn-outline-primary" onclick="setExpireDays(3)">3天</button>
                                <button type="button" class="btn btn-outline-primary" onclick="setExpireDays(7)">7天</button>
                                <button type="button" class="btn btn-outline-primary active" onclick="setExpireDays(30)">30天</button>
                                <button type="button" class="btn btn-outline-primary" onclick="setExpireDays(0)">永久</button>
                            </div>
                        </div>
                        <div class="input-group">
                            <input type="number" class="form-control" id="linkExpiresDays" value="30" min="0" max="365" placeholder="自定义天数">
                            <span class="input-group-text">天</span>
                        </div>
                        <div class="form-text">选择快捷时间或自定义天数，0表示永久有效</div>
                    </div>

                    <!-- 访问密钥 -->
                    <div class="mb-3">
                        <label for="linkAccessKey" class="form-label">访问密钥</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="linkAccessKey" readonly>
                            <button class="btn btn-outline-secondary" type="button" onclick="generateAccessKey()">
                                <i class="bi bi-arrow-clockwise"></i> 重新生成
                            </button>
                        </div>
                        <div class="form-text">4位字母数字组合，客户访问时需要输入</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitCreateLink()">创建链接</button>
            </div>
        </div>
    </div>
</div>



<script>
let currentClientId = null;

// 全局遮罩清理函数
function clearModalBackdrops() {
    // 清除所有遮罩层
    const backdrops = document.querySelectorAll('.modal-backdrop');
    backdrops.forEach(backdrop => backdrop.remove());

    // 恢复body样式
    document.body.classList.remove('modal-open');
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';

    console.log('已清理模态框遮罩层');
}

// 页面加载完成后绑定事件
document.addEventListener('DOMContentLoaded', function() {
    // 绑定排序按钮事件
    document.querySelectorAll('.sort-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const sortBy = this.getAttribute('data-sort');

            // 更新按钮状态
            document.querySelectorAll('.sort-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');

            // 更新隐藏字段
            document.getElementById('sortByInput').value = sortBy;

            // 提交表单
            document.getElementById('searchForm').submit();
        });
    });

    // 监听所有模态框的隐藏事件
    document.querySelectorAll('.modal').forEach(modal => {
        modal.addEventListener('hidden.bs.modal', function() {
            setTimeout(clearModalBackdrops, 100);
        });
    });
});

// 查看客户的所有分享链接
function viewClientShareLinks(clientId) {
    fetch(`/simple/api/clients/${clientId}/share-links/all`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const links = data.links;
                const modalBody = document.getElementById('shareLinkModalBody');
                const modalTitle = document.querySelector('#shareLinkModal .modal-title');

                // 更新标题
                modalTitle.textContent = '分享链接管理';

                let linksHtml = '';
                if (links && links.length > 0) {
                    linksHtml = `
                        <div class="mb-3">
                            <button type="button" class="btn btn-success btn-sm" onclick="showCreateLinkForm(${clientId})">
                                <i class="bi bi-plus"></i> 创建新链接
                            </button>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th width="140">创建时间</th>
                                        <th width="100">有效期</th>
                                        <th width="80">访问密钥</th>
                                        <th width="120">适用任务</th>
                                        <th width="80">状态</th>
                                        <th width="200">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                    `;

                    links.forEach(link => {
                        const isExpired = link.is_expired;
                        const isActive = link.is_active;
                        const statusBadge = !isActive ? '<span class="badge bg-secondary">已禁用</span>' :
                                          isExpired ? '<span class="badge bg-danger">已过期</span>' :
                                          '<span class="badge bg-success">有效</span>';

                        linksHtml += `
                            <tr>
                                <td><small>${new Date(link.created_at).toLocaleString()}</small></td>
                                <td><small>${link.expires_at ? new Date(link.expires_at).toLocaleDateString() : '永久'}</small></td>
                                <td>
                                    ${link.access_key ? `<code>${link.access_key}</code>` : '<span class="text-muted">无</span>'}
                                </td>
                                <td><small>${link.task_name || '所有任务'}</small></td>
                                <td>${statusBadge}</td>
                                <td>
                                    <div class="d-flex gap-1">
                                        <button class="btn btn-outline-primary btn-sm" onclick="copyToClipboard('${window.location.origin}/client-review/${link.share_key}${link.access_key ? '?key=' + link.access_key : ''}')" title="复制链接">
                                            <i class="bi bi-copy"></i> 复制
                                        </button>
                                        ${isActive && !isExpired ? `
                                            <button class="btn btn-outline-info btn-sm" onclick="viewShareLinkStats('${link.share_key}')" title="查看统计">
                                                <i class="bi bi-bar-chart"></i> 统计
                                            </button>
                                        ` : ''}
                                        ${isActive ? `
                                            <button class="btn btn-outline-danger btn-sm" onclick="disableShareLink('${link.share_key}')" title="删除链接">
                                                <i class="bi bi-trash"></i> 删除
                                            </button>
                                        ` : ''}
                                    </div>
                                </td>
                            </tr>
                        `;
                    });

                    linksHtml += `
                                </tbody>
                            </table>
                        </div>
                    `;
                } else {
                    linksHtml = `
                        <div class="text-center py-4">
                            <i class="bi bi-link-45deg fs-1 text-muted"></i>
                            <p class="text-muted">还没有创建分享链接</p>
                            <button type="button" class="btn btn-success" onclick="showCreateLinkForm(${clientId})">
                                <i class="bi bi-plus"></i> 创建第一个分享链接
                            </button>
                        </div>
                    `;
                }

                modalBody.innerHTML = linksHtml;

                const modal = new bootstrap.Modal(document.getElementById('shareLinkModal'));
                modal.show();
            } else {
                showToast(data.message, 'error');
            }
        })
        .catch(error => {
            showToast('获取分享链接失败: ' + error.message, 'error');
        });
}

// 查看单个分享链接统计
function viewShareLinkStats(shareKey) {
    fetch(`/simple/api/share-links/${shareKey}/stats`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const stats = data.stats;
                const modalBody = document.getElementById('shareLinkModalBody');
                const modalTitle = document.querySelector('#shareLinkModal .modal-title');

                // 更新标题
                modalTitle.textContent = '分享链接统计';

                // 构建完整的分享链接（包含访问密钥）
                let shareUrl = `${window.location.origin}/client-review/${shareKey}`;
                if (stats.share_link && stats.share_link.access_key) {
                    shareUrl += `?key=${stats.share_link.access_key}`;
                }

                modalBody.innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>链接信息</h6>
                            <p><strong>分享密钥:</strong><br><code>/client-review/${shareKey}</code></p>
                            <p><strong>状态:</strong> ${stats.is_expired ? '<span class="badge bg-danger">已过期</span>' : '<span class="badge bg-success">有效</span>'}</p>
                            ${stats.days_remaining !== null ? `<p><strong>剩余天数:</strong> ${stats.days_remaining} 天</p>` : ''}
                        </div>
                        <div class="col-md-6">
                            <h6>使用统计</h6>
                            <p><strong>待审核文案:</strong> ${stats.pending_count} 篇</p>
                            <p><strong>已审核文案:</strong> ${stats.reviewed_count} 篇</p>
                            <p><strong>总文案数:</strong> ${stats.total_count} 篇</p>
                        </div>
                    </div>
                    <div class="mt-3">
                        <h6>分享链接</h6>
                        <div class="input-group">
                            <input type="text" class="form-control" value="${shareUrl}" readonly>
                            <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('${shareUrl}')">
                                <i class="bi bi-copy"></i> 复制
                            </button>
                        </div>
                    </div>
                `;
                
                const modal = new bootstrap.Modal(document.getElementById('shareLinkModal'));
                modal.show();
            } else {
                showToast(data.message, 'error');
            }
        })
        .catch(error => {
            showToast('获取链接详情失败: ' + error.message, 'error');
        });
}

// 复制分享链接
function copyShareLink(shareUrl) {
    copyToClipboard(shareUrl);
}

// 复制到剪贴板 - 确保全局可用
window.copyToClipboard = function(text) {
    // 判断复制的内容类型
    let message = '内容已复制到剪贴板';
    if (text.startsWith('http')) {
        message = '链接已复制到剪贴板';
    } else if (text.length === 4 && /^[A-Z0-9]+$/.test(text)) {
        message = '访问密钥已复制到剪贴板';
    }

    // 检查是否支持现代剪贴板API（需要HTTPS或localhost）
    if (navigator.clipboard && navigator.clipboard.writeText && (window.isSecureContext || location.protocol === 'https:' || location.hostname === 'localhost')) {
        navigator.clipboard.writeText(text).then(function() {
            showToast(message, 'success');
        }, function(err) {
            console.warn('现代剪贴板API失败，使用降级方案:', err);
            // 降级方案
            fallbackCopyTextToClipboard(text, message);
        });
    } else {
        console.log('使用降级复制方案 (HTTP环境或不支持现代API)');
        // 降级方案
        fallbackCopyTextToClipboard(text, message);
    }
}

// 降级复制方案 - 兼容HTTP环境
function fallbackCopyTextToClipboard(text, message) {
    console.log('执行降级复制方案，文本:', text);

    // 方法1: 尝试使用 textarea + execCommand
    const textArea = document.createElement('textarea');
    textArea.value = text;

    // 设置样式确保元素可见但不影响布局
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    textArea.style.width = '2em';
    textArea.style.height = '2em';
    textArea.style.padding = '0';
    textArea.style.border = 'none';
    textArea.style.outline = 'none';
    textArea.style.boxShadow = 'none';
    textArea.style.background = 'transparent';

    document.body.appendChild(textArea);

    try {
        // 确保元素获得焦点
        textArea.focus();
        textArea.select();

        // 设置选择范围
        if (textArea.setSelectionRange) {
            textArea.setSelectionRange(0, text.length);
        }

        // 尝试复制
        const successful = document.execCommand('copy');
        console.log('execCommand 复制结果:', successful);

        if (successful) {
            showToast(message, 'success');
        } else {
            // 方法2: 显示手动复制提示
            showManualCopyDialog(text, message);
        }
    } catch (err) {
        console.error('execCommand 复制失败:', err);
        // 方法2: 显示手动复制提示
        showManualCopyDialog(text, message);
    } finally {
        document.body.removeChild(textArea);
    }
}

// 显示手动复制对话框
function showManualCopyDialog(text, message) {
    // 创建模态框显示文本供用户手动复制
    const modalHtml = `
        <div class="modal fade" id="manualCopyModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">手动复制</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p class="text-muted">由于浏览器安全限制，请手动复制以下内容：</p>
                        <div class="form-group">
                            <textarea class="form-control" rows="3" readonly onclick="this.select()">${text}</textarea>
                        </div>
                        <small class="text-info">点击文本框可全选内容，然后按 Ctrl+C (Windows) 或 Cmd+C (Mac) 复制</small>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('manualCopyModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新模态框
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('manualCopyModal'));
    modal.show();

    // 自动选中文本
    setTimeout(() => {
        const textarea = document.querySelector('#manualCopyModal textarea');
        if (textarea) {
            textarea.focus();
            textarea.select();
        }
    }, 500);
}

// 分享链接管理 - 确保全局可用
window.createShareLink = function(clientId) {
    console.log('打开分享链接管理，客户ID:', clientId);
    currentClientId = clientId;

    // 首先显示该客户的所有分享链接
    viewClientShareLinks(clientId);
}

// 显示创建新链接的表单
window.showCreateLinkForm = function(clientId) {
    currentClientId = clientId;

    // 更新模态框标题
    const modalTitle = document.querySelector('#createLinkModal .modal-title');
    modalTitle.textContent = '创建新的分享链接';

    // 先获取系统默认设置
    fetch('/simple/api/system/share-link-defaults')
        .then(response => response.json())
        .then(data => {
            let defaultExpires = 0; // 后备默认值
            let defaultLabel = '永久有效';

            if (data.success) {
                defaultExpires = data.defaults.expires_days;
                defaultLabel = data.defaults.expires_label;
            }

            // 重置表单内容，使用系统默认值
            const modalBody = document.querySelector('#createLinkModal .modal-body');
            modalBody.innerHTML = `
                <form id="createLinkForm">
                    <!-- 任务选择 -->
                    <div class="mb-3">
                        <label for="linkTaskId" class="form-label">选择任务</label>
                        <select class="form-select" id="linkTaskId">
                            <option value="">所有任务</option>
                            <!-- 任务选项将通过JavaScript动态加载 -->
                        </select>
                        <div class="form-text">选择特定任务或留空表示所有任务</div>
                    </div>

                    <!-- 有效期选择 -->
                    <div class="mb-3">
                        <label class="form-label">有效期</label>
                        <div class="mb-2">
                            <div class="btn-group w-100" role="group">
                                <button type="button" class="btn btn-outline-primary ${defaultExpires === 3 ? 'active' : ''}" onclick="setExpireDays(3)">3天</button>
                                <button type="button" class="btn btn-outline-primary ${defaultExpires === 7 ? 'active' : ''}" onclick="setExpireDays(7)">7天</button>
                                <button type="button" class="btn btn-outline-primary ${defaultExpires === 30 ? 'active' : ''}" onclick="setExpireDays(30)">30天</button>
                                <button type="button" class="btn btn-outline-primary ${defaultExpires === 0 ? 'active' : ''}" onclick="setExpireDays(0)">永久</button>
                            </div>
                        </div>
                        <div class="input-group">
                            <input type="number" class="form-control" id="linkExpiresDays" value="${defaultExpires}" min="0" max="365" placeholder="自定义天数">
                            <span class="input-group-text">天</span>
                        </div>
                        <div class="form-text">系统默认：${defaultLabel}，可选择快捷时间或自定义天数，0表示永久有效</div>
                    </div>

                    <!-- 访问密钥 -->
                    <div class="mb-3">
                        <label for="linkAccessKey" class="form-label">访问密钥</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="linkAccessKey" readonly>
                            <button class="btn btn-outline-secondary" type="button" onclick="generateAccessKey()">
                                <i class="bi bi-arrow-clockwise"></i> 重新生成
                            </button>
                        </div>
                        <div class="form-text">4位字母数字组合，客户访问时需要输入</div>
                    </div>
                </form>
            `;

            // 完成表单初始化后的操作
            initializeCreateLinkForm(clientId);
        })
        .catch(error => {
            console.error('获取系统默认设置失败:', error);
            // 使用后备默认值
            showCreateLinkFormFallback(clientId);
        });
}

// 后备方案：使用硬编码的默认值
function showCreateLinkFormFallback(clientId) {
    const modalBody = document.querySelector('#createLinkModal .modal-body');
    modalBody.innerHTML = `
        <form id="createLinkForm">
            <!-- 任务选择 -->
            <div class="mb-3">
                <label for="linkTaskId" class="form-label">选择任务</label>
                <select class="form-select" id="linkTaskId">
                    <option value="">所有任务</option>
                </select>
                <div class="form-text">选择特定任务或留空表示所有任务</div>
            </div>

            <!-- 有效期选择 -->
            <div class="mb-3">
                <label class="form-label">有效期</label>
                <div class="mb-2">
                    <div class="btn-group w-100" role="group">
                        <button type="button" class="btn btn-outline-primary" onclick="setExpireDays(3)">3天</button>
                        <button type="button" class="btn btn-outline-primary" onclick="setExpireDays(7)">7天</button>
                        <button type="button" class="btn btn-outline-primary" onclick="setExpireDays(30)">30天</button>
                        <button type="button" class="btn btn-outline-primary active" onclick="setExpireDays(0)">永久</button>
                    </div>
                </div>
                <div class="input-group">
                    <input type="number" class="form-control" id="linkExpiresDays" value="0" min="0" max="365" placeholder="自定义天数">
                    <span class="input-group-text">天</span>
                </div>
                <div class="form-text">选择快捷时间或自定义天数，0表示永久有效</div>
            </div>

            <!-- 访问密钥 -->
            <div class="mb-3">
                <label for="linkAccessKey" class="form-label">访问密钥</label>
                <div class="input-group">
                    <input type="text" class="form-control" id="linkAccessKey" readonly>
                    <button class="btn btn-outline-secondary" type="button" onclick="generateAccessKey()">
                        <i class="bi bi-arrow-clockwise"></i> 重新生成
                    </button>
                </div>
                <div class="form-text">4位字母数字组合，客户访问时需要输入</div>
            </div>
        </form>
    `;

    initializeCreateLinkForm(clientId);
}

// 初始化创建链接表单
function initializeCreateLinkForm(clientId) {

    // 重置底部按钮
    const modalFooter = document.querySelector('#createLinkModal .modal-footer');
    modalFooter.innerHTML = `
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" onclick="submitCreateLink()">创建链接</button>
    `;

    // 生成默认访问密钥
    generateAccessKey();

    // 加载客户的任务列表
    loadClientTasks(clientId);

    const modal = new bootstrap.Modal(document.getElementById('createLinkModal'));
    modal.show();

    // 监听创建链接模态框的关闭事件
    document.getElementById('createLinkModal').addEventListener('hidden.bs.modal', function () {
        // 如果分享链接管理模态框还在显示，也关闭它
        const shareLinkModal = bootstrap.Modal.getInstance(document.getElementById('shareLinkModal'));
        if (shareLinkModal) {
            shareLinkModal.hide();
        }
    }, { once: true }); // 只监听一次
}

// 生成访问密钥
window.generateAccessKey = function() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 4; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    document.getElementById('linkAccessKey').value = result;
    console.log('生成访问密钥:', result);
}

// 设置有效期天数
window.setExpireDays = function(days) {
    document.getElementById('linkExpiresDays').value = days;

    // 更新按钮状态
    const buttons = document.querySelectorAll('#createLinkModal .btn-group .btn');
    buttons.forEach(btn => btn.classList.remove('active'));

    // 找到对应的按钮并激活
    const targetBtn = Array.from(buttons).find(btn =>
        btn.textContent.includes(days === 0 ? '永久' : days + '天')
    );
    if (targetBtn) {
        targetBtn.classList.add('active');
    }

    console.log('设置有效期:', days === 0 ? '永久' : days + '天');
}

// 加载客户任务列表
function loadClientTasks(clientId) {
    fetch(`/simple/api/clients/${clientId}/tasks`)
        .then(response => response.json())
        .then(data => {
            const taskSelect = document.getElementById('linkTaskId');
            taskSelect.innerHTML = '<option value="">所有任务</option>';

            if (data.success && data.tasks) {
                data.tasks.forEach(task => {
                    const option = document.createElement('option');
                    option.value = task.id;
                    option.textContent = task.name;
                    taskSelect.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('加载任务列表失败:', error);
        });
}

// 提交创建链接 - 确保全局可用
window.submitCreateLink = function() {
    const expiresDays = document.getElementById('linkExpiresDays').value;
    const taskId = document.getElementById('linkTaskId').value;
    const accessKey = document.getElementById('linkAccessKey').value;

    if (!accessKey) {
        showToast('请生成访问密钥', 'error');
        return;
    }

    // 显示加载状态
    const submitBtn = document.querySelector('#createLinkModal .btn-primary');
    const originalText = submitBtn.innerHTML;
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="spinner-border spinner-border-sm me-2"></i>创建中...';

    const formData = new FormData();
    formData.append('expires_days', expiresDays || '0');
    formData.append('access_key', accessKey);
    if (taskId) {
        formData.append('task_id', taskId);
    }

    fetch(`/simple/api/clients/${currentClientId}/share-links`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 自动复制链接到剪贴板
            copyToClipboard(data.link_info.share_url);

            // 显示创建成功的链接，但不关闭当前模态框
            showCreateLinkSuccessInModal(data.link_info);
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        showToast('创建链接失败: ' + error.message, 'error');
    })
    .finally(() => {
        // 恢复按钮状态
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
}

// 刷新分享链接
function refreshShareLink(clientId) {
    if (!confirm('确定要刷新分享链接吗？旧链接将失效。')) {
        return;
    }
    
    const formData = new FormData();
    formData.append('expires_days', '30'); // 默认30天
    
    fetch(`/simple/api/clients/${clientId}/share-links/refresh`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            location.reload();
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        showToast('刷新链接失败: ' + error.message, 'error');
    });
}

// 刷新单个分享链接
window.refreshSingleShareLink = function(shareKey) {
    if (!confirm('确定要刷新这个分享链接吗？刷新后原链接将失效。')) {
        return;
    }

    // 显示加载状态
    const refreshBtn = document.querySelector(`[onclick="refreshSingleShareLink('${shareKey}')"]`);
    if (refreshBtn) {
        refreshBtn.disabled = true;
        refreshBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 刷新中...';
    }

    fetch(`/simple/api/share-links/${shareKey}/refresh`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('分享链接已刷新', 'success');
            // 重新加载分享链接列表
            viewClientShareLinks(currentClientId);
        } else {
            showToast(data.message, 'error');
            // 恢复按钮状态
            if (refreshBtn) {
                refreshBtn.disabled = false;
                refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 刷新';
            }
        }
    })
    .catch(error => {
        showToast('刷新链接失败: ' + error.message, 'error');
        // 恢复按钮状态
        if (refreshBtn) {
            refreshBtn.disabled = false;
            refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 刷新';
        }
    })
    .finally(() => {
        // 确保清除所有可能的遮罩层
        setTimeout(() => {
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => backdrop.remove());
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        }, 100);
    });
}

// 删除分享链接
window.disableShareLink = function(shareKey) {
    if (!confirm('确定要删除这个分享链接吗？删除后客户将无法访问。')) {
        return;
    }

    // 显示加载状态
    const deleteBtn = document.querySelector(`[onclick="disableShareLink('${shareKey}')"]`);
    if (deleteBtn) {
        deleteBtn.disabled = true;
        deleteBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 删除中...';
    }

    fetch(`/simple/api/share-links/${shareKey}/disable`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('分享链接已删除', 'success');
            // 重新加载分享链接列表
            viewClientShareLinks(currentClientId);
        } else {
            showToast(data.message, 'error');
            // 恢复按钮状态
            if (deleteBtn) {
                deleteBtn.disabled = false;
                deleteBtn.innerHTML = '<i class="bi bi-trash"></i> 删除';
            }
        }
    })
    .catch(error => {
        showToast('删除链接失败: ' + error.message, 'error');
        // 恢复按钮状态
        if (deleteBtn) {
            deleteBtn.disabled = false;
            deleteBtn.innerHTML = '<i class="bi bi-trash"></i> 删除';
        }
    })
    .finally(() => {
        // 确保清除所有可能的遮罩层
        setTimeout(() => {
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => backdrop.remove());
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        }, 100);
    });
}



// 在当前模态框中显示创建成功的结果
function showCreateLinkSuccessInModal(linkInfo) {
    // 隐藏表单，显示成功信息
    const modalBody = document.querySelector('#createLinkModal .modal-body');
    const modalTitle = document.querySelector('#createLinkModal .modal-title');
    const modalFooter = document.querySelector('#createLinkModal .modal-footer');

    // 更新标题
    modalTitle.textContent = '分享链接创建成功';

    // 更新内容
    modalBody.innerHTML = `
        <div class="alert alert-success">
            <i class="bi bi-check-circle"></i> 分享链接创建成功！链接已自动复制到剪贴板。
        </div>
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">分享链接信息</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label"><strong>访问链接</strong></label>
                    <div class="input-group">
                        <input type="text" class="form-control" value="${linkInfo.share_url}" readonly>
                        <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('${linkInfo.share_url}')">
                            <i class="bi bi-copy"></i> 复制
                        </button>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label"><strong>访问密钥</strong></label>
                    <div class="input-group">
                        <input type="text" class="form-control text-center fw-bold fs-4" value="${linkInfo.access_key}" readonly>
                        <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('${linkInfo.access_key}')">
                            <i class="bi bi-copy"></i> 复制
                        </button>
                    </div>
                    <div class="form-text">请将此密钥提供给客户，访问时需要输入</div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>有效期:</strong> ${linkInfo.expires_at ? linkInfo.expires_at : '永久有效'}</p>
                        <p><strong>适用任务:</strong> ${linkInfo.task_name || '所有任务'}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>创建时间:</strong> ${linkInfo.created_at}</p>
                        <p><strong>状态:</strong> <span class="badge bg-success">有效</span></p>
                    </div>
                </div>
            </div>
        </div>
        <div class="alert alert-info mt-3">
            <i class="bi bi-info-circle"></i>
            <strong>使用说明：</strong><br>
            1. 将链接发送给客户<br>
            2. 客户访问链接时需要输入访问密钥：<code>${linkInfo.access_key}</code><br>
            3. 客户可以查看和审核指定范围内的文案
        </div>
    `;

    // 更新底部按钮 - 只保留关闭按钮
    modalFooter.innerHTML = `
        <button type="button" class="btn btn-secondary" onclick="closeAllShareLinkModals()">关闭</button>
    `;
}

// 关闭所有分享链接相关的模态框
window.closeAllShareLinkModals = function() {
    // 关闭创建链接模态框
    const createLinkModal = bootstrap.Modal.getInstance(document.getElementById('createLinkModal'));
    if (createLinkModal) {
        createLinkModal.hide();
    }

    // 关闭分享链接管理模态框
    const shareLinkModal = bootstrap.Modal.getInstance(document.getElementById('shareLinkModal'));
    if (shareLinkModal) {
        shareLinkModal.hide();
    }
}

// 显示创建链接成功的结果（原函数保留，用于其他地方）
function showCreateLinkSuccess(linkInfo) {
    const modalBody = document.getElementById('shareLinkModalBody');
    modalBody.innerHTML = `
        <div class="alert alert-success">
            <i class="bi bi-check-circle"></i> 分享链接创建成功！
        </div>
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">分享链接信息</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label"><strong>访问链接</strong></label>
                    <div class="input-group">
                        <input type="text" class="form-control" value="${linkInfo.share_url}" readonly>
                        <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('${linkInfo.share_url}')">
                            <i class="bi bi-copy"></i> 复制
                        </button>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label"><strong>访问密钥</strong></label>
                    <div class="input-group">
                        <input type="text" class="form-control text-center fw-bold fs-4" value="${linkInfo.access_key}" readonly>
                        <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('${linkInfo.access_key}')">
                            <i class="bi bi-copy"></i> 复制
                        </button>
                    </div>
                    <div class="form-text">请将此密钥提供给客户，访问时需要输入</div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>有效期:</strong> ${linkInfo.expires_at ? linkInfo.expires_at : '永久有效'}</p>
                        <p><strong>适用任务:</strong> ${linkInfo.task_name || '所有任务'}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>创建时间:</strong> ${linkInfo.created_at}</p>
                        <p><strong>状态:</strong> <span class="badge bg-success">有效</span></p>
                    </div>
                </div>
            </div>
        </div>
        <div class="alert alert-info mt-3">
            <i class="bi bi-info-circle"></i>
            <strong>使用说明：</strong><br>
            1. 将链接发送给客户<br>
            2. 客户访问链接时需要输入访问密钥：<code>${linkInfo.access_key}</code><br>
            3. 客户可以查看和审核指定范围内的文案
        </div>
    `;

    const modal = new bootstrap.Modal(document.getElementById('shareLinkModal'));
    modal.show();

    // 刷新页面数据
    setTimeout(() => {
        location.reload();
    }, 1000);
}

// 显示提示消息
function showToast(message, type = 'info') {
    // 创建Toast容器（如果不存在）
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }

    // 创建Toast元素
    const toastId = 'toast-' + Date.now();
    const toastHtml = `
        <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'primary'} border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    `;

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    // 显示Toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: 3000
    });
    toast.show();

    // 自动清理
    toastElement.addEventListener('hidden.bs.toast', function() {
        toastElement.remove();
    });
}

// 服务器端搜索支持
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('serverSearchInput');
    if (searchInput) {
        // 支持Ctrl+F快捷键聚焦搜索框
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                searchInput.focus();
                searchInput.select();
            }
        });

        // 自动聚焦搜索框（如果有搜索词）
        if (searchInput.value.trim()) {
            searchInput.focus();
        }
    }
});

console.log('客户审核管理页面已加载');
</script>

<!-- 引入HTTP环境剪贴板修复方案 -->
<script src="/static/js/http-clipboard-fix.js"></script>

<!-- 引入通用剪贴板工具 -->
<script src="/static/js/clipboard-utils.js"></script>
